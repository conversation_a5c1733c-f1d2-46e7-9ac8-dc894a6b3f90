.ingredient-adder-container {
  padding: 20px;
  border-radius: 10px;
  background-color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.ingredient-adder-container form {
  flex: 1;
  overflow-y: auto;
  padding-right: 5px;
}

.ingredient-adder-container h3 {
  margin-top: 0;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 20px;
  flex-shrink: 0;
  font-weight: 600;
  background-color: #f0f7ff;
  padding: 12px 15px;
  border-radius: 8px;
  border-bottom: 2px solid #e0e0e0;
}

.ingredient-adder-container div:not(.month-checkbox-container):not(.month-checkbox-item):not(.needs-calculating-container) {
  margin-bottom: 15px;
}

.ingredient-adder-container label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.ingredient-adder-container input[type="text"],
.ingredient-adder-container input[type="number"],
.ingredient-adder-container select {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 1rem;
  background-color: white;
  transition: border-color 0.2s ease;
}

.ingredient-adder-container input[type="text"]:focus,
.ingredient-adder-container input[type="number"]:focus,
.ingredient-adder-container select:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.ingredient-adder-container input[type="checkbox"] {
  margin-right: 8px; /* Increased margin for checkbox */
  transform: scale(1.2); /* Slightly larger checkbox */
}

.ingredient-adder-container .month-checkbox-container {
  display: grid; /* Use grid for better alignment */
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); /* Responsive grid */
  gap: 10px;
  margin-bottom: 15px; /* Add margin below the container */
}

.ingredient-adder-container .month-checkbox-item {
  display: flex;
  align-items: center;
  background-color: #fff; /* Add background to items for clarity */
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #eee;
}

.ingredient-adder-container .month-checkbox-item label {
  font-weight: normal;
  margin-left: 5px;
  margin-bottom: 0;
  color: #333;
  cursor: pointer; /* Add cursor pointer to label */
}

.ingredient-adder-container button[type="submit"] {
  padding: 12px 24px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
  width: 100%;
  margin-top: 15px;
  flex-shrink: 0;
}

.ingredient-adder-container button[type="submit"]:hover {
  background-color: #388e3c;
}

.success-message {
  color: #155724; /* Darker green for success text */
  background-color: #d4edda; /* Lighter green background */
  border: 1px solid #c3e6cb; /* Green border */
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
  text-align: center; /* Center message */
}

.error-message {
  color: #721c24; /* Darker red for error text */
  background-color: #f8d7da; /* Lighter red background */
  border: 1px solid #f5c6cb; /* Red border */
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
  text-align: center; /* Center message */
}

/* Styles for new button-like checkboxes */
.season-toggle-button,
.needs-calculating-toggle-button {
  display: inline-block;
  padding: 6px 10px;
  margin: 3px;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
  font-size: 0.8rem;
  text-align: center;
}

.season-toggle-button.selected,
.needs-calculating-toggle-button.selected {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.season-toggle-button:not(.selected):hover,
.needs-calculating-toggle-button:not(.selected):hover {
  background-color: #f0f0f0;
}

.month-buttons-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-bottom: 12px;
}

.needs-calculating-container {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.needs-calculating-container label {
  margin-right: 10px; /* Space between label and button */
  margin-bottom: 0; /* Align with button */
}
