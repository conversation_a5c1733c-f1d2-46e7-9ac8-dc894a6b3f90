.ingredient-adder-container {
  padding: 15px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.ingredient-adder-container form {
  flex: 1;
  overflow-y: auto;
  padding-right: 5px;
}

.ingredient-adder-container h3 {
  margin-top: 0;
  color: #333;
  text-align: center;
  margin-bottom: 15px;
  flex-shrink: 0;
}

.ingredient-adder-container div:not(.month-checkbox-container):not(.month-checkbox-item):not(.needs-calculating-container) {
  margin-bottom: 12px;
}

.ingredient-adder-container label {
  display: block;
  margin-bottom: 8px; /* Increased space below label */
  font-weight: bold;
  color: #555; /* Slightly lighter label color */
}

.ingredient-adder-container input[type="text"],
.ingredient-adder-container input[type="number"],
.ingredient-adder-container select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 0.9rem;
}

.ingredient-adder-container input[type="checkbox"] {
  margin-right: 8px; /* Increased margin for checkbox */
  transform: scale(1.2); /* Slightly larger checkbox */
}

.ingredient-adder-container .month-checkbox-container {
  display: grid; /* Use grid for better alignment */
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); /* Responsive grid */
  gap: 10px;
  margin-bottom: 15px; /* Add margin below the container */
}

.ingredient-adder-container .month-checkbox-item {
  display: flex;
  align-items: center;
  background-color: #fff; /* Add background to items for clarity */
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #eee;
}

.ingredient-adder-container .month-checkbox-item label {
  font-weight: normal;
  margin-left: 5px;
  margin-bottom: 0;
  color: #333;
  cursor: pointer; /* Add cursor pointer to label */
}

.ingredient-adder-container button[type="submit"] {
  padding: 12px 20px; /* Increased padding */
  background-color: #28a745; /* Green color for submit */
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem; /* Consistent font size */
  transition: background-color 0.3s ease; /* Smooth transition */
  display: block; /* Make button block to center it */
  margin: 20px auto 0; /* Center button and add top margin */
}

.ingredient-adder-container button[type="submit"]:hover {
  background-color: #218838; /* Darker green on hover */
}

.success-message {
  color: #155724; /* Darker green for success text */
  background-color: #d4edda; /* Lighter green background */
  border: 1px solid #c3e6cb; /* Green border */
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
  text-align: center; /* Center message */
}

.error-message {
  color: #721c24; /* Darker red for error text */
  background-color: #f8d7da; /* Lighter red background */
  border: 1px solid #f5c6cb; /* Red border */
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
  text-align: center; /* Center message */
}

/* Styles for new button-like checkboxes */
.season-toggle-button,
.needs-calculating-toggle-button {
  display: inline-block;
  padding: 6px 10px;
  margin: 3px;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
  font-size: 0.8rem;
  text-align: center;
}

.season-toggle-button.selected,
.needs-calculating-toggle-button.selected {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.season-toggle-button:not(.selected):hover,
.needs-calculating-toggle-button:not(.selected):hover {
  background-color: #f0f0f0;
}

.month-buttons-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-bottom: 12px;
}

.needs-calculating-container {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.needs-calculating-container label {
  margin-right: 10px; /* Space between label and button */
  margin-bottom: 0; /* Align with button */
}
