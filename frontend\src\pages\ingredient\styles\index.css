/* Ingredient page specific styling */
.ingredient-container {
  width: 100%;
  height: 100vh;
  padding: 20px;
  background-color: #f5f5f5;
  box-sizing: border-box;
  overflow: hidden;
}

.ingredient-main-layout {
  display: flex;
  gap: 20px;
  height: 100%;
  max-width: 1400px;
  margin: 0 auto;
}

.ingredient-adder-section {
  flex: 0 0 450px;
  height: 100%;
  overflow: hidden;
}

.ingredient-table-section {
  flex: 1;
  height: 100%;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 20px;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.ingredient-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Remove any nested .table-container margins */
.ingredient-list .table-container {
  margin: 0;
  border-radius: 8px;
  box-shadow: none;
  padding: 0;
}

.table-container {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
}

.table-responsive {
  flex: 1;
  overflow: auto;
  min-height: 0;
}

/* Type tabs styling */
.type-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
  flex-shrink: 0;
}

.type-tab {
  padding: 8px 16px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
}

.type-tab.active {
  background-color: #6a994e;
  color: white;
  border-color: #386641;
}

/* Table styling */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  overflow: hidden;
}

th {
  background-color: #f0f7ff;
  padding: 12px 15px;
  text-align: left;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #e0e0e0;
}

td {
  padding: 10px 15px;
  border-bottom: 1px solid #e0e0e0;
}

tr:last-child td {
  border-bottom: none;
}

tr:hover {
  background-color: #f9f9f9;
}

/* Animation for row transitions */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

tr {
  animation: fadeIn 0.3s ease;
}

/* Edit and action buttons */
.edit-btn,
.save-btn,
.cancel-btn {
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-right: 5px;
}

.edit-btn {
  background-color: #2196f3;
  color: white;
}

.edit-btn:hover {
  background-color: #1976d2;
}

.save-btn {
  background-color: #4caf50;
  color: white;
}

.save-btn:hover {
  background-color: #388e3c;
}

.cancel-btn {
  background-color: #f44336;
  color: white;
}

.cancel-btn:hover {
  background-color: #d32f2f;
}

/* Responsive design for mobile */
@media (max-width: 768px) {
  .ingredient-container {
    padding: 10px;
  }

  .ingredient-main-layout {
    flex-direction: column;
    gap: 15px;
  }

  .ingredient-adder-section {
    flex: none;
    height: auto;
    max-height: 40vh;
  }

  .ingredient-table-section {
    flex: 1;
    min-height: 50vh;
  }
}
