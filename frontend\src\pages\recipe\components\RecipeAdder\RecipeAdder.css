/* Styling for the RecipeAdder component */
.recipe-adder-container {
  padding: 15px;
  border-radius: 10px;
  background-color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.recipe-adder-container h2 {
  margin-top: 0;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 15px;
  flex-shrink: 0;
  font-weight: 600;
  font-size: 1.1rem;
  background-color: #f0f7ff;
  padding: 8px 12px;
  border-radius: 6px;
  border-bottom: 2px solid #e0e0e0;
}

.recipe-adder-form {
  flex: 1;
  overflow-y: auto;
  padding-right: 5px;
  display: flex;
  flex-direction: column;
}

.form-sections-container {
  display: flex;
  gap: 15px;
  flex: 1;
  min-height: 0;
}

.recipe-adder-form .form-section {
  background-color: #fff;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.recipe-details-section {
  flex: 0 0 45%;
}

.ingredients-section {
  flex: 0 0 50%;
}

.recipe-adder-form .form-section h3 {
  color: #2c3e50;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 6px;
  margin-bottom: 10px;
  font-size: 1rem;
  font-weight: 600;
}

.recipe-adder-form .form-group {
  margin-bottom: 10px;
}

.recipe-adder-form label {
  display: block;
  margin-bottom: 4px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.recipe-adder-form input[type="text"],
.recipe-adder-form input[type="number"],
.recipe-adder-form textarea,
.recipe-adder-form select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 0.9rem;
  background-color: white;
  transition: border-color 0.2s ease;
}

.recipe-adder-form input[type="text"]:focus,
.recipe-adder-form input[type="number"]:focus,
.recipe-adder-form textarea:focus,
.recipe-adder-form select:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.recipe-adder-form input[type="file"] {
  padding: 3px;
  font-size: 0.9rem;
}

.recipe-adder-form textarea {
  resize: vertical;
  min-height: 60px;
}

.recipe-adder-form .image-preview {
  max-width: 120px;
  max-height: 120px;
  margin-top: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.recipe-adder-form .months-selection {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 5px;
}

.recipe-adder-form .month-button {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s;
}

.recipe-adder-form .month-button.selected {
  background-color: #4CAF50;
  color: white;
  border-color: #4CAF50;
}

.recipe-adder-form .month-button:hover:not(.selected) {
  background-color: #e0e0e0;
}

.recipe-adder-form input[type="checkbox"] {
  margin-right: 5px;
  vertical-align: middle;
}

.ingredient-search-results {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
  margin-bottom: 15px;
  max-height: 150px; /* Limit height and make scrollable if needed */
  overflow-y: auto;
  padding: 5px;
  border: 1px solid #eee;
  border-radius: 4px;
}

.ingredient-add-button {
  padding: 4px 8px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 0.8rem;
  margin: 2px;
}

.ingredient-add-button:hover {
  background-color: #0056b3;
}

.ingredient-add-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.selected-ingredients-list {
  list-style-type: none;
  padding: 0;
  max-height: 200px;
  overflow-y: auto;
}

.selected-ingredient-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px;
  border: 1px solid #e0e0e0;
  border-radius: 3px;
  margin-bottom: 4px;
  background-color: #fdfdfd;
  font-size: 0.8rem;
}

.selected-ingredient-item span {
  flex-grow: 1;
}

.ingredient-controls {
  display: flex;
  align-items: center;
  gap: 6px;
}

.ingredient-controls .ingredient-quantity {
  width: 60px;
  padding: 4px;
  font-size: 0.8rem;
}

.ingredient-controls .ingredient-unit {
  padding: 4px;
  min-width: 70px;
  font-size: 0.8rem;
}

.ingredient-controls .ingredient-divisible-label {
  font-weight: normal;
  font-size: 0.9em;
  display: flex;
  align-items: center;
}

.ingredient-controls .remove-ingredient-button {
  padding: 4px 8px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 0.8rem;
}

.ingredient-controls .remove-ingredient-button:hover {
  background-color: #c82333;
}

.feedback-message {
  margin-top: 15px;
  padding: 10px;
  border-radius: 4px;
  text-align: center;
}

.feedback-message.success-message {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.feedback-message.error-message {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.submit-recipe-button {
  display: block;
  width: 100%;
  padding: 10px 20px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: auto;
  margin-bottom: 0;
  flex-shrink: 0;
}

.submit-recipe-button:hover {
  background-color: #388e3c;
}

.submit-recipe-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.ingredient-quantity-input {
  width: 70px;
  padding: 6px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 0.8rem;
  margin-left: 5px;
}
